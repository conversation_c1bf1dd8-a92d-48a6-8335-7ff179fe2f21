/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    background-color: #34495e;
    color: white;
    padding: 1rem 0;
    text-align: center;
    margin-bottom: 30px;
    border-radius: 5px;
}

header h1 {
    margin-bottom: 10px;
}

.logo {
    text-align: center;
    margin-bottom: 1rem;
}

/* Navigation Styles */
nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

nav li, .main-nav li {
    margin: 0 15px;
}

nav a, .main-nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.3s;
}

nav a:hover, nav a.active, .main-nav a:hover, .main-nav a.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Page Container Styles */
.page-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.page-title {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 2rem;
}

.page-content {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Main Content Layout */
.main-content {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    flex-shrink: 0;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    height: fit-content;
    max-height: 80vh;
    overflow-y: auto;
}

.left-sidebar {
    width: 250px;
}

.right-sidebar {
    width: 300px;
}

.filter-section h3, .summary-section h3 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #2c3e50;
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.filter-actions button {
    flex: 1;
    padding: 8px;
    font-size: 14px;
}

.cipher-filters {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 60vh;
    overflow-y: auto;
}

.cipher-category-header {
    font-weight: bold;
    font-size: 15px;
    color: #2c3e50;
    margin-top: 15px;
    margin-bottom: 5px;
    padding: 5px 8px;
    background-color: #f0f7ff;
    border-radius: 4px;
    border-left: 3px solid #3498db;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.cipher-category-header:hover {
    background-color: #d4e6f7;
}

.cipher-category-header::after {
    content: '▼';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    transition: transform 0.2s;
}

.cipher-category-header.collapsed::after {
    transform: translateY(-50%) rotate(-90deg);
}

.cipher-category-items {
    display: block;
    margin-bottom: 10px;
}

.cipher-filter-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
    margin-left: 10px;
}

.cipher-filter-item:hover {
    background-color: #f0f7ff;
}

.cipher-filter-item input {
    margin-right: 10px;
    cursor: pointer;
}

.cipher-filter-item label {
    cursor: pointer;
    font-size: 14px;
    flex: 1;
}

/* Summary Styles */
#summary-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 70vh;
    overflow-y: auto;
}

.summary-category-header {
    font-weight: bold;
    font-size: 14px;
    color: #2c3e50;
    margin-top: 15px;
    margin-bottom: 5px;
    padding: 5px 8px;
    background-color: #f0f7ff;
    border-radius: 4px;
    border-left: 3px solid #3498db;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.summary-category-header:hover {
    background-color: #d4e6f7;
}

.summary-category-header::after {
    content: '▼';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    transition: transform 0.2s;
}

.summary-category-header.collapsed::after {
    transform: translateY(-50%) rotate(-90deg);
}

.summary-category-items {
    display: block;
    margin-bottom: 10px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
    border-left: 3px solid #3498db;
    font-size: 14px;
    transition: background-color 0.2s;
    margin-left: 10px;
}

.summary-item:hover {
    background-color: #f0f7ff;
}

.summary-name {
    font-weight: 500;
}

.summary-values {
    font-weight: bold;
    color: #2c3e50;
}

/* Main Content Styles */
main {
    flex: 1;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.input-section {
    margin-bottom: 20px;
}

textarea {
    width: 100%;
    height: 100px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    resize: vertical;
    margin-bottom: 10px;
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

.results-section h2 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.empty-result {
    color: #999;
    font-style: italic;
}

/* Results Display Styles */
.cipher-result {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border-left: 4px solid #3498db;
}

.cipher-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    align-items: center;
}

.cipher-name-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.cipher-name {
    font-weight: bold;
    font-size: 18px;
}

.cipher-category-badge {
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 12px;
    background-color: #e8f4fc;
    color: #3498db;
    font-weight: 500;
}

.cipher-value {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    min-width: 60px;
    text-align: right;
}

.cipher-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.word-breakdown {
    margin-top: 15px;
}

.word-breakdown h4 {
    margin-bottom: 15px;
    font-size: 16px;
    color: #555;
}

.word-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-start;
}

.word-item {
    background-color: #e8f4fc;
    padding: 12px 15px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.word-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding-bottom: 5px;
}

.word-text {
    font-weight: 500;
}

.word-value {
    font-weight: bold;
    color: #3498db;
}

.letter-breakdown {
    font-family: monospace;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

/* Footer Styles */
footer {
    background-color: #34495e;
    color: white;
    padding: 1rem 0;
    margin-top: 2rem;
    border-radius: 5px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links a {
    color: white;
    margin-left: 1rem;
    text-decoration: none;
}

.footer-links a:hover {
    text-decoration: underline;
}

.footer-nav {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-nav ul {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    list-style: none;
}

.footer-nav li {
    margin: 5px 15px;
}

.footer-nav a {
    color: white;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-nav a:hover {
    color: #e0e0e0;
    text-decoration: underline;
}

/* Blog Styles */
.blog-post {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.blog-post-title {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.blog-post-meta {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.blog-post-content {
    line-height: 1.6;
}

.read-more {
    display: inline-block;
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    transition: background-color 0.3s;
}

.read-more:hover {
    background-color: #2980b9;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-group textarea {
    min-height: 150px;
}

.form-submit {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.form-submit:hover {
    background-color: #2980b9;
}

.contact-form {
    margin-bottom: 2rem;
}

.contact-info, .faq-section {
    margin-top: 3rem;
}

.faq-item {
    margin-bottom: 1.5rem;
}

.faq-item h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

/* Grid Layout for Results */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Hide Calculate Button when Auto-Calculate is enabled */
.hidden {
    display: none;
}

/* Modified Responsive Styles */
@media (max-width: 1200px) {
    .main-content {
        flex-wrap: wrap;
    }

    main {
        order: 1;
        width: 100%;
    }

    .left-sidebar {
        order: 2;
        width: 100%;
    }

    .right-sidebar {
        order: 3;
        width: 100%;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        margin-top: 1rem;
    }
}

@media (max-width: 900px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: none;
    }

    .left-sidebar {
        order: 1;
    }

    main {
        order: 2;
    }

    .right-sidebar {
        order: 3;
    }

    .cipher-filters, #summary-container {
        max-height: 300px;
    }

    nav ul {
        flex-wrap: wrap;
    }

    nav li, .main-nav li {
        margin: 5px 10px;
    }
}

/* Add to your existing CSS */
.filter-search {
    margin-bottom: 15px;
}

.filter-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.filter-search input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.cipher-filters {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 5px; /* Add some space for scrollbar */
}

/* Scrollbar styling */
.cipher-filters::-webkit-scrollbar {
    width: 8px;
}

.cipher-filters::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.cipher-filters::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.cipher-filters::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Download Page Specific Styles */
.app-intro {
    font-size: 1.2em;
    font-weight: 500;
    color: #444;
    margin-bottom: 2em;
    text-align: center;
    line-height: 1.5;
}

.download-cards {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin: 40px 0;
}

.download-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    padding: 30px;
    width: 300px;
    text-align: center;
    transition: transform 0.3s ease;
    border: 1px solid #eaeaea;
}

.download-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0,0,0,0.15);
}

.app-icon {
    width: 120px;
    height: 120px;
    margin-bottom: 20px;
    border-radius: 22%;
    object-fit: cover;
}

.download-card h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.4em;
}

.download-card p {
    color: #666;
    margin-bottom: 25px;
    min-height: 60px;
    line-height: 1.5;
}

.download-btn {
    display: inline-block;
    color: white;
    padding: 12px 25px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.ios-btn {
    background: #007AFF;
}

.ios-btn:hover {
    background: #0062CC;
}

.android-btn {
    background: #3DDC84;
}

.android-btn:hover {
    background: #2BB673;
}

.coming-soon {
    background: #999 !important;
    cursor: not-allowed;
}

.badge {
    position: relative;
    top: -5px;
    background: #FF3B30;
    color: white;
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    margin-left: 5px;
}

.app-features {
    margin: 50px 0;
}

.feature-list {
    columns: 2;
    column-gap: 40px;
    margin: 20px 0;
}

.feature-list li {
    margin-bottom: 12px;
    line-height: 1.5;
    break-inside: avoid;
}

.app-description {
    background: #f9f9f9;
    padding: 25px;
    border-radius: 8px;
    margin: 30px 0;
    line-height: 1.7;
}

.app-description p {
    margin-bottom: 15px;
}

.system-requirements {
    margin: 40px 0;
}

.requirements-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

.requirement {
    background: #f5f5f5;
    padding: 20px;
    border-radius: 8px;
}

.requirement h3 {
    color: #333;
    margin-bottom: 10px;
}

.requirement p {
    color: #666;
    margin-bottom: 8px;
    line-height: 1.5;
}

.support-links {
    text-align: center;
    margin-top: 40px;
    color: #666;
}

.support-links a {
    color: #007AFF;
    text-decoration: none;
}

.support-links a:hover {
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .feature-list {
        columns: 1;
    }
    
    .requirements-grid {
        grid-template-columns: 1fr;
    }
    
    .download-card {
        width: 100%;
        max-width: 350px;
    }
}

/* QR Code Section Styles */
.download-options {
    margin-bottom: 40px;
}

.qr-section {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

.qr-code {
    width: 150px;
    height: 150px;
    margin: 0 auto 10px;
    display: block;
    border: 1px solid #eaeaea;
    padding: 10px;
    background: white;
}

.qr-caption {
    font-size: 0.9em;
    color: #666;
    margin-top: 10px;
}

/* Coming Soon QR placeholder */
.qr-section.coming-soon {
    opacity: 0.7;
}

.qr-section.coming-soon .qr-code {
    filter: grayscale(100%);
}

/* Responsive adjustments for QR codes */
@media (max-width: 768px) {
    .qr-code {
        width: 120px;
        height: 120px;
    }
}